import React from 'react';
import Link from 'next/link';
import { <PERSON><PERSON><PERSON><PERSON>, Clock, User } from 'lucide-react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import SimplePageLayout from '@/components/Layout/SimplePageLayout';

// Sample articles data
const articles = {
  'add-trustee': {
    title: 'How do I add a trustee to my account?',
    content: `
      <h2>Adding a Trustee to Your Legalock Account</h2>
      
      <p>Trustees are essential to your digital legacy plan. They are the people who will have access to your information after your passing. Here's how to add them:</p>
      
      <h3>Step 1: Navigate to Trustees</h3>
      <p>From your dashboard, click on the "Trustees" card or use the navigation menu to go to the Trustees section.</p>
      
      <h3>Step 2: Add New Trustee</h3>
      <p>Click the "Add Trustee" button and fill in the required information:</p>
      <ul>
        <li>First and Last Name</li>
        <li>Email Address</li>
        <li>Phone Number (optional but recommended)</li>
        <li>Relationship to you</li>
      </ul>
      
      <h3>Step 3: Send Invitation</h3>
      <p>After adding the trustee information, you can send them an invitation email. They will receive instructions on how to create their account and accept their trustee role.</p>
      
      <h3>Important Notes:</h3>
      <ul>
        <li>Free accounts can have up to 1 trustee</li>
        <li>Premium accounts can have up to 5 trustees</li>
        <li>Trustees cannot access your account while you are alive</li>
        <li>You can edit or remove trustees at any time</li>
      </ul>
      
      <p>For more information about trustee management, visit our <a href="/help/category/trustees-contacts">Trustees & Contacts</a> help section.</p>
    `,
    lastUpdated: '2024-01-15',
    author: 'Legalock Support Team'
  },
  'data-after-passing': {
    title: 'What happens to my data after I pass away?',
    content: `
      <h2>Your Data After Passing</h2>
      
      <p>Understanding what happens to your data after you pass away is crucial for digital legacy planning. Here's how Legalock handles this process:</p>
      
      <h3>Verification Process</h3>
      <p>When a trustee believes you have passed away, they must provide verification through one of these methods:</p>
      <ul>
        <li>Death certificate</li>
        <li>Obituary notice</li>
        <li>Legal documentation</li>
      </ul>
      
      <h3>Data Access</h3>
      <p>Once verification is complete, your trustees will gain access to:</p>
      <ul>
        <li>Your digital assets list with access instructions</li>
        <li>Documents stored in your Digital Vault</li>
        <li>Emergency contacts information</li>
        <li>Your last wishes and final messages</li>
        <li>Service sunset instructions</li>
        <li>Time capsules (if any were created)</li>
      </ul>
      
      <h3>Data Security</h3>
      <p>Your data remains encrypted and secure throughout this process. Only verified trustees can access your information, and all access is logged for security purposes.</p>
      
      <h3>Data Retention</h3>
      <p>Your data will be maintained for your trustees according to our data retention policy. Trustees can download important documents and information for their records.</p>
    `,
    lastUpdated: '2024-01-10',
    author: 'Legalock Support Team'
  },
  'vault-security': {
    title: 'How secure is the Digital Vault?',
    content: `
      <h2>Digital Vault Security</h2>
      
      <p>The security of your documents is our top priority. Here's how we protect your Digital Vault:</p>
      
      <h3>Encryption</h3>
      <p>All documents are encrypted using industry-standard AES-256 encryption:</p>
      <ul>
        <li>Files are encrypted before upload</li>
        <li>Encryption keys are stored separately from your data</li>
        <li>Only you and your verified trustees can decrypt files</li>
      </ul>
      
      <h3>Access Control</h3>
      <p>Strict access controls ensure only authorized users can view your documents:</p>
      <ul>
        <li>Multi-factor authentication required</li>
        <li>Role-based access for trustees</li>
        <li>All access attempts are logged and monitored</li>
      </ul>
      
      <h3>Infrastructure Security</h3>
      <p>Our infrastructure follows best practices:</p>
      <ul>
        <li>Data stored in secure, SOC 2 compliant data centers</li>
        <li>Regular security audits and penetration testing</li>
        <li>Automated backups with geographic redundancy</li>
        <li>24/7 monitoring and threat detection</li>
      </ul>
      
      <h3>Compliance</h3>
      <p>We maintain compliance with relevant data protection regulations including GDPR and CCPA.</p>
    `,
    lastUpdated: '2024-01-12',
    author: 'Legalock Security Team'
  },
  'change-subscription': {
    title: 'Can I change my subscription plan?',
    content: `
      <h2>Changing Your Subscription Plan</h2>

      <p>Yes, you can upgrade or downgrade your subscription plan at any time. Here's how:</p>

      <h3>Upgrading to Premium</h3>
      <p>To upgrade from Essential Legacy (Free) to Legacy Preserver (Premium):</p>
      <ol>
        <li>Go to your Dashboard</li>
        <li>Click on "Upgrade" in the subscription section</li>
        <li>Choose the Legacy Preserver plan</li>
        <li>Complete the payment process</li>
      </ol>

      <h3>Downgrading to Free</h3>
      <p>To downgrade from Premium to Free:</p>
      <ol>
        <li>Go to Settings > Subscription</li>
        <li>Click "Cancel Subscription"</li>
        <li>Your premium features will remain active until the end of your billing period</li>
        <li>After that, you'll be moved to the free plan</li>
      </ol>

      <h3>Important Notes</h3>
      <ul>
        <li>Upgrades take effect immediately</li>
        <li>Downgrades take effect at the end of your billing period</li>
        <li>You may need to remove excess data when downgrading (e.g., extra trustees, documents)</li>
        <li>No refunds for partial months when downgrading</li>
      </ul>
    `,
    lastUpdated: '2024-01-08',
    author: 'Legalock Support Team'
  },
  'trustee-verification': {
    title: 'How do trustees verify my passing?',
    content: `
      <h2>Trustee Verification Process</h2>

      <p>When a trustee believes you have passed away, they must go through a verification process to access your digital legacy information.</p>

      <h3>Required Documentation</h3>
      <p>Trustees must provide one of the following documents:</p>
      <ul>
        <li><strong>Death Certificate:</strong> Official document from the vital records office</li>
        <li><strong>Obituary:</strong> Published obituary from a recognized news source</li>
        <li><strong>Legal Documentation:</strong> Court documents or legal notices</li>
      </ul>

      <h3>Verification Steps</h3>
      <ol>
        <li>Trustee logs into their Legalock account</li>
        <li>Navigates to the "Verify Passing" section</li>
        <li>Uploads required documentation</li>
        <li>Provides additional context if needed</li>
        <li>Submits verification request</li>
      </ol>

      <h3>Review Process</h3>
      <p>Our team reviews verification requests within 24-48 hours:</p>
      <ul>
        <li>Documents are verified for authenticity</li>
        <li>Information is cross-referenced</li>
        <li>Trustee is notified of approval or if additional information is needed</li>
      </ul>

      <h3>After Verification</h3>
      <p>Once verified, trustees gain access to your digital legacy information and can begin fulfilling their responsibilities according to your wishes.</p>
    `,
    lastUpdated: '2024-01-05',
    author: 'Legalock Support Team'
  },
  'plan-differences': {
    title: 'What is the difference between free and premium plans?',
    content: `
      <h2>Free vs Premium Plan Comparison</h2>

      <p>Legalock offers two subscription tiers to meet different needs. Here's a detailed comparison:</p>

      <h3>Essential Legacy (Free Plan)</h3>
      <ul>
        <li><strong>Trustees:</strong> Up to 1 trustee</li>
        <li><strong>Digital Assets:</strong> Unlimited</li>
        <li><strong>Emergency Contacts:</strong> Unlimited</li>
        <li><strong>Service Sunset:</strong> Unlimited</li>
        <li><strong>Last Wishes:</strong> Full access</li>
        <li><strong>Digital Vault:</strong> 1 document</li>
        <li><strong>Time Capsules:</strong> Not available</li>
        <li><strong>Will Advisor:</strong> 1 consultation</li>
        <li><strong>Support:</strong> Email support</li>
      </ul>

      <h3>Legacy Preserver (Premium Plan - $29.99/year)</h3>
      <ul>
        <li><strong>Trustees:</strong> Up to 5 trustees</li>
        <li><strong>Digital Assets:</strong> Unlimited</li>
        <li><strong>Emergency Contacts:</strong> Unlimited</li>
        <li><strong>Service Sunset:</strong> Unlimited</li>
        <li><strong>Last Wishes:</strong> Full access</li>
        <li><strong>Digital Vault:</strong> Up to 5GB storage</li>
        <li><strong>Time Capsules:</strong> Up to 100 time capsules (10GB total)</li>
        <li><strong>Will Advisor:</strong> Unlimited consultations</li>
        <li><strong>Support:</strong> Priority email support</li>
      </ul>

      <h3>Which Plan Should I Choose?</h3>
      <p>Choose the <strong>Free Plan</strong> if you:</p>
      <ul>
        <li>Have a simple digital legacy</li>
        <li>Only need one trustee</li>
        <li>Don't need to store many documents</li>
        <li>Don't want to create time capsules</li>
      </ul>

      <p>Choose the <strong>Premium Plan</strong> if you:</p>
      <ul>
        <li>Want multiple trustees for redundancy</li>
        <li>Need to store important documents securely</li>
        <li>Want to create time capsules for future delivery</li>
        <li>Have a complex digital estate</li>
      </ul>
    `,
    lastUpdated: '2024-01-14',
    author: 'Legalock Support Team'
  },
  'upload-documents': {
    title: 'How do I upload documents to the Digital Vault?',
    content: `
      <h2>Uploading Documents to Your Digital Vault</h2>

      <p>The Digital Vault allows you to securely store important documents that your trustees will need. Here's how to upload them:</p>

      <h3>Step 1: Access the Digital Vault</h3>
      <p>From your dashboard, click on the "Digital Vault" card or navigate to the Vault section.</p>

      <h3>Step 2: Upload Documents</h3>
      <ol>
        <li>Click the "Upload Document" button</li>
        <li>Select files from your computer (or drag and drop)</li>
        <li>Choose a category for organization</li>
        <li>Add a description (optional but recommended)</li>
        <li>Click "Upload" to save</li>
      </ol>

      <h3>Supported File Types</h3>
      <p>You can upload most common file types including:</p>
      <ul>
        <li>PDF documents (.pdf)</li>
        <li>Images (.jpg, .png, .gif)</li>
        <li>Microsoft Office files (.doc, .docx, .xls, .xlsx)</li>
        <li>Text files (.txt)</li>
        <li>And many more</li>
      </ul>

      <h3>Storage Limits</h3>
      <ul>
        <li><strong>Free Plan:</strong> 1 document maximum</li>
        <li><strong>Premium Plan:</strong> Up to 5GB total storage</li>
        <li><strong>File Size:</strong> Maximum 100MB per file</li>
      </ul>

      <h3>Security</h3>
      <p>All documents are encrypted before upload and can only be accessed by you and your verified trustees after your passing.</p>

      <h3>Organization Tips</h3>
      <ul>
        <li>Use descriptive filenames</li>
        <li>Organize by category (Legal, Financial, Personal, etc.)</li>
        <li>Add descriptions to provide context</li>
        <li>Regularly review and update documents</li>
      </ul>
    `,
    lastUpdated: '2024-01-11',
    author: 'Legalock Support Team'
  }
};

interface PageProps {
  params: {
    slug: string;
  };
}

export default function QuestionPage({ params }: PageProps) {
  const article = articles[params.slug as keyof typeof articles];

  if (!article) {
    return (
      <SimplePageLayout title="Article Not Found">
        <div className="text-center py-12">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Article Not Found</h2>
          <p className="text-gray-600 mb-6">The article you're looking for doesn't exist.</p>
          <Button asChild>
            <Link href="/help">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Help Center
            </Link>
          </Button>
        </div>
      </SimplePageLayout>
    );
  }

  return (
    <SimplePageLayout title={article.title}>
      <div className="mb-6">
        <Button variant="outline" asChild>
          <Link href="/help">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Help Center
          </Link>
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="text-2xl">{article.title}</CardTitle>
          <div className="flex items-center gap-4 text-sm text-gray-500">
            <div className="flex items-center">
              <User className="h-4 w-4 mr-1" />
              {article.author}
            </div>
            <div className="flex items-center">
              <Clock className="h-4 w-4 mr-1" />
              Last updated: {new Date(article.lastUpdated).toLocaleDateString()}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div
            className="prose prose-blue max-w-none"
            dangerouslySetInnerHTML={{ __html: article.content }}
          />
        </CardContent>
      </Card>

      <div className="mt-8 bg-gray-100 rounded-lg p-6">
        <h3 className="text-lg font-semibold mb-4">Was this article helpful?</h3>
        <div className="flex gap-4">
          <Button variant="outline">Yes, it was helpful</Button>
          <Button variant="outline">No, I need more help</Button>
        </div>
        <p className="text-sm text-gray-600 mt-4">
          Still need help? <Link href="/contact" className="text-primary hover:underline">Contact our support team</Link>
        </p>
      </div>
    </SimplePageLayout>
  );
}
